<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab切换调试</title>
    <style>
        /* 基础样式 */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        /* Tab容器 */
        .tabs-container {
            display: flex;
            border: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        /* Tab导航 */
        .tabs-nav {
            width: 200px;
            background-color: #f5f5f5;
            border-right: 1px solid #ddd;
        }
        
        .tab {
            padding: 15px;
            cursor: pointer;
            border-bottom: 1px solid #ddd;
        }
        
        .tab:hover {
            background-color: #e9e9e9;
        }
        
        .tab.active {
            background-color: #fff;
            border-left: 3px solid #4a90e2;
            font-weight: bold;
        }
        
        /* Tab内容区域 */
        .tab-content-area {
            flex: 1;
            padding: 20px;
        }
        
        /* Tab内容 */
        .tab-content {
            display: none !important;
        }
        
        .tab-content.active {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
        }
    </style>
</head>
<body>
    <h1>Tab切换调试页面</h1>
    
    <!-- Tab容器 -->
    <div class="tabs-container">
        <!-- Tab导航 -->
        <div class="tabs-nav">
            <div class="tab active" onclick="switchTab('tab1')">Tab 1</div>
            <div class="tab" onclick="switchTab('tab2')">Tab 2</div>
            <div class="tab" onclick="switchTab('tab3')">Tab 3</div>
        </div>
        
        <!-- Tab内容区域 -->
        <div class="tab-content-area">
            <!-- Tab 1 内容 -->
            <div id="tab1-tab" class="tab-content active">
                <h2>Tab 1 内容</h2>
                <p>这是Tab 1的内容区域。默认显示此Tab。</p>
            </div>
            
            <!-- Tab 2 内容 -->
            <div id="tab2-tab" class="tab-content">
                <h2>Tab 2 内容</h2>
                <p>这是Tab 2的内容区域。点击Tab 2导航可以显示此内容。</p>
            </div>
            
            <!-- Tab 3 内容 -->
            <div id="tab3-tab" class="tab-content">
                <h2>Tab 3 内容</h2>
                <p>这是Tab 3的内容区域。点击Tab 3导航可以显示此内容。</p>
            </div>
        </div>
    </div>
    
    <div>
        <h2>调试信息</h2>
        <div id="debug-info" style="border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9; height: 200px; overflow: auto;"></div>
    </div>
    
    <script>
        // 调试日志函数
        function log(message) {
            const debugInfo = document.getElementById('debug-info');
            const logEntry = document.createElement('div');
            logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            debugInfo.appendChild(logEntry);
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        // Tab切换函数 - 方法1：使用classList和style.display
        function switchTab(tabId) {
            log(`切换到Tab: ${tabId}`);
            
            // 获取所有tab内容和导航
            const tabContents = document.querySelectorAll('.tab-content');
            const tabs = document.querySelectorAll('.tab');
            
            // 移除所有tab的激活状态
            tabs.forEach(tab => {
                tab.classList.remove('active');
                log(`移除Tab导航激活状态`);
            });
            
            // 隐藏所有tab内容
            tabContents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
                log(`隐藏Tab内容: ${content.id}`);
            });
            
            // 激活选中的tab导航
            const selectedTabNav = document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`);
            if (selectedTabNav) {
                selectedTabNav.classList.add('active');
                log(`激活Tab导航: ${tabId}`);
            }
            
            // 激活选中的tab内容
            const selectedTab = document.getElementById(tabId + '-tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
                selectedTab.style.display = 'block';
                log(`激活Tab内容: ${tabId}-tab`);
                
                // 输出计算后的样式
                const computedStyle = window.getComputedStyle(selectedTab);
                log(`计算后的display: ${computedStyle.display}`);
                log(`计算后的visibility: ${computedStyle.visibility}`);
                log(`计算后的opacity: ${computedStyle.opacity}`);
            } else {
                log(`错误: 找不到Tab内容 ${tabId}-tab`);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            // 确保默认Tab正确显示
            const defaultTab = document.querySelector('.tab-content.active');
            if (defaultTab) {
                defaultTab.style.display = 'block';
                log(`设置默认Tab显示: ${defaultTab.id}`);
                
                // 输出计算后的样式
                const computedStyle = window.getComputedStyle(defaultTab);
                log(`默认Tab计算后的display: ${computedStyle.display}`);
                log(`默认Tab计算后的visibility: ${computedStyle.visibility}`);
                log(`默认Tab计算后的opacity: ${computedStyle.opacity}`);
            }
        });
    </script>
</body>
</html>