<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab测试</title>
    <style>
        .tab-content {
            display: none !important;
            padding: 20px;
            border: 1px solid #ccc;
            margin-top: 10px;
        }
        
        .tab-content.active {
            display: block !important;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid #ccc;
            display: inline-block;
            margin-right: 5px;
        }
        
        .tab.active {
            background-color: #e6f4ff;
            border-color: #1677ff;
        }
    </style>
</head>
<body>
    <div class="tabs-nav">
        <div class="tab active" onclick="switchTab('tab1')">Tab 1</div>
        <div class="tab" onclick="switchTab('tab2')">Tab 2</div>
        <div class="tab" onclick="switchTab('tab3')">Tab 3</div>
    </div>
    
    <div class="tab-content-area">
        <div id="tab1-tab" class="tab-content active">Tab 1 内容</div>
        <div id="tab2-tab" class="tab-content">Tab 2 内容</div>
        <div id="tab3-tab" class="tab-content">Tab 3 内容</div>
    </div>
    
    <script>
        // Tab切换函数
        function switchTab(tabId) {
            console.log('切换到Tab:', tabId);
            
            // 获取所有tab内容和导航
            const tabContents = document.querySelectorAll('.tab-content');
            const tabs = document.querySelectorAll('.tab');
            
            // 隐藏所有tab内容，移除所有tab的激活状态
            tabContents.forEach(content => {
                content.classList.remove('active');
                // 强制设置display为none，覆盖!important
                content.setAttribute('style', 'display: none !important');
            });
            
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 激活选中的tab
            const selectedTab = document.getElementById(tabId + '-tab');
            const selectedTabNav = document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`);
            
            if (selectedTab) {
                selectedTab.classList.add('active');
                // 强制设置display为block，覆盖!important
                selectedTab.setAttribute('style', 'display: block !important');
                console.log('显示Tab内容:', tabId + '-tab', selectedTab);
            } else {
                console.error('找不到Tab内容:', tabId + '-tab');
            }
            
            if (selectedTabNav) {
                selectedTabNav.classList.add('active');
            }
        }
    </script>
</body>
</html>