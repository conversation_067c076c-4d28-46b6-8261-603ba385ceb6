
1. 需求背景
为响应邮政新一年的政策指导，并提升“微邮付”业务的商户质量与合规水平，旨在建立一套系统化的商户健康度评估模型。通过对新商户的准入风险和存量商户的交易风险进行有效识别与评估，平台可以更精准地进行商户管理，保障业务长期、健康、稳定发展。
2.  需求目标
建立一个集资料审查、风险识别、任务下发、状态追踪于一体的商户健康度管理工具，核心目标是：
1. 有效识别：精准识别出具有潜在风险的“不健康”商户。
2. 驱动优化：通过下发整改、走访等任务，优先帮助和提升不健康的商户，使其转为健康状态。
3. 提升质量：持续优化平台整体的商户结构和质量，实现商户与平台的合作共赢。
3. 功能逻辑
3.1 名词解释
术语	解释
新商户	指商户进件通过后第二天，平台需要进行审查与准入风险评估的商户。
存量商户	指平台全量的商户（商户状态正常），按月进行周期性的交易风险评估。
风险等级	分为高风险、中风险、低风险。商户的综合风险等级由其触发的最高风险指标等级决定。
审查状态	针对新商户，标识其被审查和处理的进度。包括：待审查、待整改、整改中、已完成。

3.2 全局规则
3.2.1 角色与权限
系统用户角色分为省、市、区/县、网点四级。
- 数据权限：所有角色遵循层级管理原则，只能查看和操作本级及下级机构的商户数据。
- 操作权限：各级角色的操作权限基本一致，均可查看详情、下发任务。客户经理主要负责执行被下发的任务。
3.2.2 风险等级定义
- 根据预设的《风险指标细则》（见3.4节）自动计算每个商户触发的风险项及其等级。
- 一个商户的最终风险等级，取其所有触发的风险项中最高的等级。
  - 例：商户A同时触发了“门头照历史重复次数（中风险）”和“交易时间集中（高风险）”，则商户A的最终风险等级为“高风险”。
3.3 功能模块一：商户审查与风险评估 (新商户)
此模块旨在对新进件商户进行独立的审查与风险评估，并根据结果选择是否下发整改或走访任务，保证流程清晰，便于后续追踪。
3.3.1 业务流程
graph TD
    A[商户进件审核成功] --> B[T+1 系统自动展示在审查列表，状态为待审查];
    B --> C[进入详情页，执行审查和风险评估];
    C --> D{审查结果/风险等级判定};
    D -- 无问题 --> E[无需整改，更新审查状态为“已完成”];
    E --> F[流程结束，可支持再次审查];
    D -- 整改/中高风险 --> G[选择下发整改/走访任务];
    G -- 下发 --> H[集成现有功能支撑];
    H --> I[更新审查状态为“整改中”];
    I --> J[任务处理完成];
    J --> K[接口回调通知];
    K --> L[更新审查状态为“已完成”];
    L --> F;
3.3.2 列表页
- 菜单入口：PC端菜单 -> 商户审查与风险评估
- 筛选区：
  - 商户号
  - 商户名称
  - 负责人手机号
  - 市级分公司
  - 区县分公司
  - 营业所
  - 进件时间
  - 审查时间
  - 审查状态（下拉菜单：待审查、待整改、整改中、已完成）
  - 风险等级（下拉菜单：高风险、中风险、低风险）
- 列表区：
  - 字段：商户号、商户名称、市级分公司、区县分公司、营业所、进件时间、风险等级、审查状态、操作。
  - 审查状态：需用不同颜色的标签进行醒目标识，便于用户追踪。
    - 待审查 (灰色)
    - 待整改 (橙色)
    - 整改中 (蓝色)
    - 已完成 (绿色)
  - 操作：【详情】按钮。
3.2.3 详情页
详情页通过Tab页签布局，清晰地分离审查内容、风险画像和操作历史。
- Tab 1: 审查详情
  - 内容：按模块展示商户所有基础信息、证件资料、结算信息、非法人授权。

    基础信息
    商户信息
    字段名称	类型	说明
    商户号	数值	
    商户名称	文本	商户简称
    经营行业	引用	
    商户类型	引用	个人、企业、个体工商
    当前通道	引用	
    商户地址	文本	
    商户标签		
    机构信息
    字段名称	类型	说明
    市级分公司	引用	
    区县分公司	引用	
    营业所	引用	
    店面照片
    字段名称	类型	说明
    门头照	图片	
    收银台照片	图片	
    店内场景照	图片	
    证件资料
    负责人信息（仅企业、个体工商户展示）
    字段名称	类型	说明
    负责人	文本	
    负责人手机号	数值	
    身份证号	数值	
    营业执照信息
    字段名称	类型	说明
    营业执照正面	图片	
    营业执照号	数值	
    营业地址	文本	
    是否长期执照	布尔	
    起始时间		
    法人信息（仅企业与个体工商户展示）
    字段名称	类型	说明
    身份证正面	图片	
    身份证国徽面	图片	
    姓名	文本	
    手机号	数值	
    证件类型	引用	
    证件号码	数值	
    法人地址	文本	
    是否长期证件	引用	
    证件起始时间	日期	
    证件结束时间	日期	
    结算人信息（个人、非法人展示）
    字段名称	类型	说明
    姓名	文本	
    手机号	数值	
    证件类型	引用	
    身份证号	文本	
    是否长期证件	引用	
    证件起始时间	日期	
    证件结束时间	日期	
    身份证正面	图片	
    身份证国徽面	图片	
    身份证手持	图片	
    结算银行信息
    字段名称	类型	说明
    结算银行	文本	
    开户银行	文本	
    结算联行号	数值	
    银行卡号	数值	
    银行卡正面	图片	
    非法人信息
    字段名称	类型	说明
    授权函		



  - 操作：页面右上角设置【下发整改任务】按钮。
    - 点击按钮，调用现有任务系统接口，选择整改类型，下发
    - 一个商户同时只能有一个“整改中”的任务。若已有任务，则按钮置灰，并提示“该商户有进行中的任务”。
- Tab 2: 风险详情 (商户360度风险画像)
  - 页面右上角设置【下发走访任务】按钮，点击调用现有接口，选择走访打卡，下发任务，
  - 布局：页面中心为商户图标，周围以发散状布局展示所有风险指标的“标签卡片”。
  - 标签卡片设计：
    - 卡片标题：风险类型名称（如：进件次数）。
    - 卡片内容：
      - 判定标准：简述该风险项的判断逻辑。
      - 当前情况：展示该商户在此项的具体数据。
      - 风险等级：用醒目的颜色和文字标出该项的评估结果（高风险/中风险/低风险/无风险）。
    - 示例：
- 门头照历史重复次数 判定标准：照片与存量商户重复使用次数。 当前情况：重复3次。 风险等级：<font color="orange">中风险</font>
- Tab 3: 操作历史
  - 记录所有与该商户相关的操作，包括“标记、整改、走访”等。
  - 字段：操作时间、操作人、操作类型（标记/整改/走访）、任务类型、备注 、状态。
3.3 功能模块二：存量商户风险评估
此模块旨在对全量存量商户进行周期性的交易行为风险监控。
3.3.1 业务流程
graph TD
    A[暂定每月1号] --> B{平台自动对上月全量商户<br/>进行交易风险评估};
    B --> C[生成评估报告];
    C --> D[机构人员进入列表页<br/>默认查看上月高、中风险商户];
    D --> E{筛选/查看详情};
    E --> F[批量导出资料];
    F --> G[线下核实];
    G --> H[手动处理];
    H --> I[流程结束];

3.3.2 列表页
- 菜单入口：PC端菜单 -> 存量商户风险评估
- 数据逻辑：每月1日生成上一个自然月的评估数据。
- 默认展示：默认展示上月的、风险等级为高风险和中风险的商户。
- 筛选区：
  - 商户号
  - 商户名称
  - 市级分公司
  - 区县分公司
  - 营业所
  - 风险等级（下拉菜单：高风险、中风险、低风险）
  - 评估时间（格式：YYYY-MM，选择到月即可，默认为上一月）
  
- 列表区：
  - 字段：商户号、商户名称、市级分公司、区县分公司、营业所、风险等级、评估时间、操作。
  - 操作：【详情】按钮。
- 批量操作：列表区上方设置【批量导出】按钮。
  - 点击后，将当前筛选结果下的商户核心信息导出为Excel文件。
  - 导出的字段为固定的预设字段（主要是商户的信息和风险指标评估详情）。
3.3.3 详情页
- 布局：展示商户360度风险画像。
- 内容：画像的布局和设计风格与新商户模块的“风险详情”页保持一致，但展示的风险指标为存量商户的交易类风险指标。
3.4 风险指标细则
3.4.1 新商户风险指标
见图片

3.4.2 存量商户风险指标
见图片
4. 非功能性需求
4.1 性能要求
- 列表页查询响应时间应在3秒以内。
- 存量商户的月度风险评估应通过后台定时任务在每月1日凌晨完成，不影响白天业务系统的性能。
4.2 数据要求
- 新商户数据在商户成功进件后的次日（T+1）进入本系统。
- 存量商户交易数据以自然月为周期进行归集和计算。