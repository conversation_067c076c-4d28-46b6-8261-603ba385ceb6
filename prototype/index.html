<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 商户健康度监测系统</title>
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --warning-grey: #8c8c8c;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .card-body {
            padding: 24px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            border: 1px solid transparent;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .btn-outline:hover {
            background: var(--primary-blue-light);
        }

        /* 状态标签 */
        .status-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending { background: #f5f5f5; color: var(--warning-grey); }
        .status-todo { background: #fff7e6; color: var(--warning-orange); }
        .status-processing { background: var(--primary-blue-light); color: var(--primary-blue); }
        .status-completed { background: #f6ffed; color: var(--success-green); }

        /* 风险等级标签 */
        .risk-high { background: #fff2f0; color: var(--error-red); }
        .risk-medium { background: #fff7e6; color: var(--warning-orange); }
        .risk-low { background: #f6ffed; color: var(--success-green); }

        /* 隐藏页面 */
        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* 筛选区域 */
        .filter-section {
            background: var(--bg-container);
            padding: 16px 24px;
            border-radius: 8px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .filter-row {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .filter-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        /* 表格样式 */
        .table-container {
            background: var(--bg-container);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
        }

        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
        }

        .table tr:hover {
            background: #fafafa;
        }

        /* Tab样式 */
        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab-btn:hover {
            color: var(--primary-blue);
        }

        .tab-btn.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 风险画像样式 */
        .risk-radar {
            position: relative;
            width: 100%;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .risk-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .merchant-avatar {
            width: 80px;
            height: 80px;
            background: var(--primary-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }

        .merchant-avatar i {
            font-size: 32px;
            color: white;
        }

        .merchant-info {
            background: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .merchant-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .merchant-id {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .risk-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 风险指标卡片 */
        .risk-card {
            position: absolute;
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color-light);
            transition: all 0.3s ease;
        }

        .risk-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .risk-card-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-card-header i {
            font-size: 16px;
            color: var(--primary-blue);
        }

        .risk-card-header span:nth-child(2) {
            flex: 1;
            font-weight: 500;
            color: var(--text-primary);
        }

        .risk-card-content {
            padding: 16px;
        }

        .risk-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;
        }

        .risk-item:last-child {
            margin-bottom: 0;
        }

        .risk-label {
            color: var(--text-secondary);
            min-width: 70px;
            margin-right: 8px;
        }

        .risk-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        /* 风险卡片位置 */
        .risk-card-1 { top: 10%; left: 10%; }
        .risk-card-2 { top: 10%; right: 10%; }
        .risk-card-3 { bottom: 10%; left: 10%; }
        .risk-card-4 { bottom: 10%; right: 10%; }
        .risk-card-5 { top: 50%; right: 5%; transform: translateY(-50%); }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <div style="width: 28px; height: 28px; background: var(--primary-blue); border-radius: 4px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                    <i class="fas fa-shield-alt" style="color: white; font-size: 14px;"></i>
                </div>
                微邮付监测系统
            </div>
        </div>
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">商户管理</div>
                <a href="#" class="nav-item active" onclick="showPage('merchant-review')">
                    <i class="fas fa-user-check nav-icon"></i>
                    商户审查与风险评估
                </a>
                <a href="#" class="nav-item" onclick="showPage('stock-risk')">
                    <i class="fas fa-chart-line nav-icon"></i>
                    存量商户风险评估
                </a>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 新商户审查页面 -->
        <div id="merchant-review" class="page active">
            <div class="top-header">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">商户管理</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">商户审查与风险评估</span>
                </div>
            </div>
            
            <div class="content-area">
                <h1 class="page-title">商户审查与风险评估</h1>
                
                <!-- 筛选区域 -->
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="filter-item">
                            <label class="filter-label">商户号</label>
                            <input type="text" class="filter-input" placeholder="请输入商户号">
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">商户名称</label>
                            <input type="text" class="filter-input" placeholder="请输入商户名称">
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">负责人手机号</label>
                            <input type="text" class="filter-input" placeholder="请输入手机号">
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">市级分公司</label>
                            <select class="filter-input">
                                <option value="">请选择</option>
                                <option value="beijing">北京分公司</option>
                                <option value="shanghai">上海分公司</option>
                                <option value="guangzhou">广州分公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label class="filter-label">区县分公司</label>
                            <select class="filter-input">
                                <option value="">请选择</option>
                                <option value="chaoyang">朝阳区分公司</option>
                                <option value="haidian">海淀区分公司</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">营业所</label>
                            <select class="filter-input">
                                <option value="">请选择</option>
                                <option value="office1">营业所1</option>
                                <option value="office2">营业所2</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">审查状态</label>
                            <select class="filter-input">
                                <option value="">全部</option>
                                <option value="pending">待审查</option>
                                <option value="todo">待整改</option>
                                <option value="processing">整改中</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">风险等级</label>
                            <select class="filter-input">
                                <option value="">全部</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label class="filter-label">进件时间</label>
                            <input type="date" class="filter-input">
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">审查时间</label>
                            <input type="date" class="filter-input">
                        </div>
                        <div class="filter-item" style="justify-content: flex-end; flex-direction: row; align-items: flex-end; gap: 8px;">
                            <button class="btn btn-primary">
                                <i class="fas fa-search" style="margin-right: 4px;"></i>
                                查询
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-redo" style="margin-right: 4px;"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 列表区域 -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>商户号</th>
                                <th>商户名称</th>
                                <th>市级分公司</th>
                                <th>区县分公司</th>
                                <th>营业所</th>
                                <th>进件时间</th>
                                <th>风险等级</th>
                                <th>审查状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>M202401001</td>
                                <td>张三便利店</td>
                                <td>北京分公司</td>
                                <td>朝阳区分公司</td>
                                <td>营业所1</td>
                                <td>2024-01-15</td>
                                <td><span class="status-tag risk-high">高风险</span></td>
                                <td><span class="status-tag status-pending">待审查</span></td>
                                <td>
                                    <button class="btn btn-outline" onclick="showMerchantDetail('M202401001')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>M202401002</td>
                                <td>李四超市</td>
                                <td>上海分公司</td>
                                <td>浦东新区分公司</td>
                                <td>营业所2</td>
                                <td>2024-01-16</td>
                                <td><span class="status-tag risk-medium">中风险</span></td>
                                <td><span class="status-tag status-todo">待整改</span></td>
                                <td>
                                    <button class="btn btn-outline" onclick="showMerchantDetail('M202401002')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>M202401003</td>
                                <td>王五餐厅</td>
                                <td>广州分公司</td>
                                <td>天河区分公司</td>
                                <td>营业所3</td>
                                <td>2024-01-17</td>
                                <td><span class="status-tag risk-low">低风险</span></td>
                                <td><span class="status-tag status-processing">整改中</span></td>
                                <td>
                                    <button class="btn btn-outline" onclick="showMerchantDetail('M202401003')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>M202401004</td>
                                <td>赵六药店</td>
                                <td>深圳分公司</td>
                                <td>南山区分公司</td>
                                <td>营业所4</td>
                                <td>2024-01-18</td>
                                <td><span class="status-tag risk-medium">中风险</span></td>
                                <td><span class="status-tag status-completed">已完成</span></td>
                                <td>
                                    <button class="btn btn-outline" onclick="showMerchantDetail('M202401004')">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 存量商户风险评估页面 -->
        <div id="stock-risk" class="page">
            <div class="top-header">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">商户管理</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">存量商户风险评估</span>
                </div>
            </div>

            <div class="content-area">
                <h1 class="page-title">存量商户风险评估</h1>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="filter-item">
                            <label class="filter-label">商户号</label>
                            <input type="text" class="filter-input" placeholder="请输入商户号">
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">商户名称</label>
                            <input type="text" class="filter-input" placeholder="请输入商户名称">
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">市级分公司</label>
                            <select class="filter-input">
                                <option value="">请选择</option>
                                <option value="beijing">北京分公司</option>
                                <option value="shanghai">上海分公司</option>
                                <option value="guangzhou">广州分公司</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">区县分公司</label>
                            <select class="filter-input">
                                <option value="">请选择</option>
                                <option value="chaoyang">朝阳区分公司</option>
                                <option value="haidian">海淀区分公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="filter-row">
                        <div class="filter-item">
                            <label class="filter-label">营业所</label>
                            <select class="filter-input">
                                <option value="">请选择</option>
                                <option value="office1">营业所1</option>
                                <option value="office2">营业所2</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">风险等级</label>
                            <select class="filter-input">
                                <option value="">全部</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label">评估时间</label>
                            <input type="month" class="filter-input" value="2024-01">
                        </div>
                        <div class="filter-item" style="justify-content: flex-end; flex-direction: row; align-items: flex-end; gap: 8px;">
                            <button class="btn btn-primary">
                                <i class="fas fa-search" style="margin-right: 4px;"></i>
                                查询
                            </button>
                            <button class="btn btn-outline">
                                <i class="fas fa-redo" style="margin-right: 4px;"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 操作区域 -->
                <div style="margin-bottom: 16px;">
                    <button class="btn btn-primary">
                        <i class="fas fa-download" style="margin-right: 4px;"></i>
                        批量导出
                    </button>
                </div>

                <!-- 列表区域 -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>商户号</th>
                                <th>商户名称</th>
                                <th>市级分公司</th>
                                <th>区县分公司</th>
                                <th>营业所</th>
                                <th>风险等级</th>
                                <th>评估时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>M202312001</td>
                                <td>老王烧烤店</td>
                                <td>北京分公司</td>
                                <td>朝阳区分公司</td>
                                <td>营业所1</td>
                                <td><span class="status-tag risk-high">高风险</span></td>
                                <td>2024-01</td>
                                <td>
                                    <button class="btn btn-outline" onclick="showStockDetail('M202312001')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>M202312002</td>
                                <td>小李奶茶店</td>
                                <td>上海分公司</td>
                                <td>浦东新区分公司</td>
                                <td>营业所2</td>
                                <td><span class="status-tag risk-medium">中风险</span></td>
                                <td>2024-01</td>
                                <td>
                                    <button class="btn btn-outline" onclick="showStockDetail('M202312002')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>M202312003</td>
                                <td>张姐服装店</td>
                                <td>广州分公司</td>
                                <td>天河区分公司</td>
                                <td>营业所3</td>
                                <td><span class="status-tag risk-high">高风险</span></td>
                                <td>2024-01</td>
                                <td>
                                    <button class="btn btn-outline" onclick="showStockDetail('M202312003')">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>M202312004</td>
                                <td>陈师傅修车行</td>
                                <td>深圳分公司</td>
                                <td>南山区分公司</td>
                                <td>营业所4</td>
                                <td><span class="status-tag risk-medium">中风险</span></td>
                                <td>2024-01</td>
                                <td>
                                    <button class="btn btn-outline" onclick="showStockDetail('M202312004')">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 商户详情页面 -->
        <div id="merchant-detail" class="page">
            <div class="top-header">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">商户管理</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">商户审查与风险评估</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">商户详情</span>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button class="btn btn-outline" onclick="showPage('merchant-review')">
                        <i class="fas fa-arrow-left" style="margin-right: 4px;"></i>
                        返回列表
                    </button>
                    <button class="btn btn-primary" id="task-btn">
                        <i class="fas fa-tasks" style="margin-right: 4px;"></i>
                        下发整改任务
                    </button>
                </div>
            </div>

            <div class="content-area">
                <h1 class="page-title">商户详情 - <span id="merchant-name">张三便利店</span></h1>

                <!-- Tab导航 -->
                <div class="card" style="margin-bottom: 16px;">
                    <div class="card-header" style="padding: 0;">
                        <div style="display: flex; border-bottom: 1px solid var(--border-color-light);">
                            <button class="tab-btn active" onclick="switchTab('review-detail')">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                审查详情
                            </button>
                            <button class="tab-btn" onclick="switchTab('risk-detail')">
                                <i class="fas fa-exclamation-triangle" style="margin-right: 4px;"></i>
                                风险详情
                            </button>
                            <button class="tab-btn" onclick="switchTab('operation-history')">
                                <i class="fas fa-history" style="margin-right: 4px;"></i>
                                操作历史
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tab内容 -->
                <!-- 审查详情Tab -->
                <div id="review-detail" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">基础信息</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                                <!-- 商户信息 -->
                                <div>
                                    <h4 style="margin-bottom: 16px; color: var(--text-primary); font-weight: 500;">商户信息</h4>
                                    <div style="display: grid; gap: 12px;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">商户号：</span>
                                            <span>M202401001</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">商户名称：</span>
                                            <span>张三便利店</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">经营行业：</span>
                                            <span>零售业</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">商户类型：</span>
                                            <span>个体工商户</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">当前通道：</span>
                                            <span>微信支付</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">商户地址：</span>
                                            <span>北京市朝阳区建国路88号</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 机构信息 -->
                                <div>
                                    <h4 style="margin-bottom: 16px; color: var(--text-primary); font-weight: 500;">机构信息</h4>
                                    <div style="display: grid; gap: 12px;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">市级分公司：</span>
                                            <span>北京分公司</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">区县分公司：</span>
                                            <span>朝阳区分公司</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: var(--text-secondary);">营业所：</span>
                                            <span>营业所1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 店面照片 -->
                    <div class="card" style="margin-top: 16px;">
                        <div class="card-header">
                            <h3 class="card-title">店面照片</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                                <div style="text-align: center;">
                                    <div style="width: 200px; height: 150px; background: #f5f5f5; border: 2px dashed var(--border-color); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">
                                        <i class="fas fa-image" style="font-size: 24px; color: var(--text-disabled);"></i>
                                    </div>
                                    <span style="color: var(--text-secondary); font-size: 12px;">门头照</span>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 200px; height: 150px; background: #f5f5f5; border: 2px dashed var(--border-color); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">
                                        <i class="fas fa-image" style="font-size: 24px; color: var(--text-disabled);"></i>
                                    </div>
                                    <span style="color: var(--text-secondary); font-size: 12px;">收银台照片</span>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 200px; height: 150px; background: #f5f5f5; border: 2px dashed var(--border-color); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">
                                        <i class="fas fa-image" style="font-size: 24px; color: var(--text-disabled);"></i>
                                    </div>
                                    <span style="color: var(--text-secondary); font-size: 12px;">店内场景照</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 风险详情Tab -->
                <div id="risk-detail" class="tab-content">
                    <div style="text-align: center; margin-bottom: 24px;">
                        <button class="btn btn-primary" style="margin-left: auto;">
                            <i class="fas fa-map-marker-alt" style="margin-right: 4px;"></i>
                            下发走访任务
                        </button>
                    </div>

                    <!-- 360度风险画像 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">商户风险画像</h3>
                        </div>
                        <div class="card-body">
                            <div class="risk-radar">
                                <!-- 中心商户图标 -->
                                <div class="risk-center">
                                    <div class="merchant-avatar">
                                        <i class="fas fa-store"></i>
                                    </div>
                                    <div class="merchant-info">
                                        <div class="merchant-name">张三便利店</div>
                                        <div class="merchant-id">M202401001</div>
                                        <div class="risk-level risk-high">高风险</div>
                                    </div>
                                </div>

                                <!-- 风险指标卡片 -->
                                <div class="risk-card risk-card-1">
                                    <div class="risk-card-header">
                                        <i class="fas fa-redo-alt"></i>
                                        <span>进件次数</span>
                                        <span class="risk-tag risk-high">高风险</span>
                                    </div>
                                    <div class="risk-card-content">
                                        <div class="risk-item">
                                            <span class="risk-label">判定标准：</span>
                                            <span>小微商户5次及以上入驻次数</span>
                                        </div>
                                        <div class="risk-item">
                                            <span class="risk-label">当前情况：</span>
                                            <span>该商户历史进件7次</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="risk-card risk-card-2">
                                    <div class="risk-card-header">
                                        <i class="fas fa-camera"></i>
                                        <span>门头照重复</span>
                                        <span class="risk-tag risk-medium">中风险</span>
                                    </div>
                                    <div class="risk-card-content">
                                        <div class="risk-item">
                                            <span class="risk-label">判定标准：</span>
                                            <span>与存量商户照片重复2-4次</span>
                                        </div>
                                        <div class="risk-item">
                                            <span class="risk-label">当前情况：</span>
                                            <span>门头照重复使用3次</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="risk-card risk-card-3">
                                    <div class="risk-card-header">
                                        <i class="fas fa-cash-register"></i>
                                        <span>收银台照重复</span>
                                        <span class="risk-tag risk-low">低风险</span>
                                    </div>
                                    <div class="risk-card-content">
                                        <div class="risk-item">
                                            <span class="risk-label">判定标准：</span>
                                            <span>与存量商户照片重复1次</span>
                                        </div>
                                        <div class="risk-item">
                                            <span class="risk-label">当前情况：</span>
                                            <span>收银台照重复使用1次</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="risk-card risk-card-4">
                                    <div class="risk-card-header">
                                        <i class="fas fa-building"></i>
                                        <span>场景照重复</span>
                                        <span class="risk-tag risk-medium">中风险</span>
                                    </div>
                                    <div class="risk-card-content">
                                        <div class="risk-item">
                                            <span class="risk-label">判定标准：</span>
                                            <span>与存量商户照片重复2-4次</span>
                                        </div>
                                        <div class="risk-item">
                                            <span class="risk-label">当前情况：</span>
                                            <span>场景照重复使用2次</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作历史Tab -->
                <div id="operation-history" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">操作历史</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>操作时间</th>
                                            <th>操作人</th>
                                            <th>操作类型</th>
                                            <th>任务类型</th>
                                            <th>备注</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2024-01-15 14:30:00</td>
                                            <td>张经理</td>
                                            <td>审查</td>
                                            <td>-</td>
                                            <td>初次审查，发现高风险</td>
                                            <td><span class="status-tag status-completed">已完成</span></td>
                                        </tr>
                                        <tr>
                                            <td>2024-01-15 15:00:00</td>
                                            <td>张经理</td>
                                            <td>整改</td>
                                            <td>资料补充</td>
                                            <td>要求补充门头照片</td>
                                            <td><span class="status-tag status-processing">进行中</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 存量商户详情页面 -->
        <div id="stock-detail" class="page">
            <div class="top-header">
                <div class="breadcrumb">
                    <span class="breadcrumb-item">商户管理</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">存量商户风险评估</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">商户详情</span>
                </div>
                <div style="display: flex; gap: 8px;">
                    <button class="btn btn-outline" onclick="showPage('stock-risk')">
                        <i class="fas fa-arrow-left" style="margin-right: 4px;"></i>
                        返回列表
                    </button>
                </div>
            </div>

            <div class="content-area">
                <h1 class="page-title">存量商户风险详情 - <span id="stock-merchant-name">老王烧烤店</span></h1>

                <!-- 360度风险画像 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">商户风险画像</h3>
                    </div>
                    <div class="card-body">
                        <div class="risk-radar">
                            <!-- 中心商户图标 -->
                            <div class="risk-center">
                                <div class="merchant-avatar">
                                    <i class="fas fa-store"></i>
                                </div>
                                <div class="merchant-info">
                                    <div class="merchant-name">老王烧烤店</div>
                                    <div class="merchant-id">M202312001</div>
                                    <div class="risk-level risk-high">高风险</div>
                                </div>
                            </div>

                            <!-- 存量商户风险指标卡片 -->
                            <div class="risk-card risk-card-1">
                                <div class="risk-card-header">
                                    <i class="fas fa-clock"></i>
                                    <span>交易时间</span>
                                    <span class="risk-tag risk-high">高风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>集中在1小时内交易</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>80%交易集中在22:00-23:00</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-2">
                                <div class="risk-card-header">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>大额交易</span>
                                    <span class="risk-tag risk-high">高风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>单笔交易≥5000元</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>月均大额交易15笔</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-3">
                                <div class="risk-card-header">
                                    <i class="fas fa-network-wired"></i>
                                    <span>IP异常交易</span>
                                    <span class="risk-tag risk-medium">中风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>异地IP交易2-9笔</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>检测到5笔异地IP交易</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-4">
                                <div class="risk-card-header">
                                    <i class="fas fa-users"></i>
                                    <span>交易人群</span>
                                    <span class="risk-tag risk-high">高风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>重复消费者占比≥60%</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>重复消费者占比75%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-5">
                                <div class="risk-card-header">
                                    <i class="fas fa-credit-card"></i>
                                    <span>信用卡交易占比</span>
                                    <span class="risk-tag risk-high">高风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>信用卡交易占比≥80%</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>信用卡交易占比85%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));

            // 根据页面ID设置对应的导航项为活跃状态
            if (pageId === 'merchant-review') {
                navItems[0].classList.add('active');
            } else if (pageId === 'stock-risk') {
                navItems[1].classList.add('active');
            }
        }

        // Tab切换功能
        function switchTab(tabId) {
            console.log('Tab clicked:', tabId);
            
            // 隐藏所有Tab内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 显示目标Tab内容
            document.getElementById(tabId).classList.add('active');

            // 更新Tab按钮状态
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));

            // 设置对应的Tab按钮为活跃状态
            event.target.classList.add('active');

            // 根据Tab更新右上角按钮
            const taskBtn = document.getElementById('task-btn');
            if (tabId === 'risk-detail') {
                taskBtn.innerHTML = '<i class="fas fa-map-marker-alt" style="margin-right: 4px;"></i>下发走访任务';
            } else {
                taskBtn.innerHTML = '<i class="fas fa-tasks" style="margin-right: 4px;"></i>下发整改任务';
            }
        }

        // 显示商户详情
        function showMerchantDetail(merchantId) {
            showPage('merchant-detail');

            // 模拟根据商户ID加载不同数据
            const merchantData = {
                'M202401001': { name: '张三便利店', status: '待审查' },
                'M202401002': { name: '李四超市', status: '待整改' },
                'M202401003': { name: '王五餐厅', status: '整改中' },
                'M202401004': { name: '赵六药店', status: '已完成' }
            };

            const data = merchantData[merchantId];
            if (data) {
                document.getElementById('merchant-name').textContent = data.name;
            }
        }

        // 显示存量商户详情
        function showStockDetail(merchantId) {
            showPage('stock-detail');

            // 模拟根据商户ID加载不同数据
            const stockData = {
                'M202312001': { name: '老王烧烤店' },
                'M202312002': { name: '小李奶茶店' },
                'M202312003': { name: '张姐服装店' },
                'M202312004': { name: '陈师傅修车行' }
            };

            const data = stockData[merchantId];
            if (data) {
                document.getElementById('stock-merchant-name').textContent = data.name;
            }
        }

        // 模拟任务下发功能
        document.addEventListener('DOMContentLoaded', function() {
            const taskBtn = document.getElementById('task-btn');
            if (taskBtn) {
                taskBtn.addEventListener('click', function() {
                    const currentText = this.textContent.trim();
                    if (currentText.includes('整改')) {
                        alert('整改任务已下发！');
                    } else if (currentText.includes('走访')) {
                        alert('走访任务已下发！');
                    }
                });
            }

            // 模拟批量导出功能
            const exportBtns = document.querySelectorAll('.btn');
            exportBtns.forEach(btn => {
                if (btn.textContent.includes('批量导出')) {
                    btn.addEventListener('click', function() {
                        alert('正在导出Excel文件...');
                    });
                }
            });

            // 模拟查询功能
            const searchBtns = document.querySelectorAll('.btn-primary');
            searchBtns.forEach(btn => {
                if (btn.textContent.includes('查询')) {
                    btn.addEventListener('click', function() {
                        alert('查询功能已执行！');
                    });
                }
            });

            // 模拟重置功能
            const resetBtns = document.querySelectorAll('.btn-outline');
            resetBtns.forEach(btn => {
                if (btn.textContent.includes('重置')) {
                    btn.addEventListener('click', function() {
                        // 重置所有筛选条件
                        const inputs = this.closest('.filter-section').querySelectorAll('.filter-input');
                        inputs.forEach(input => {
                            if (input.type === 'text' || input.type === 'date' || input.type === 'month') {
                                input.value = '';
                            } else if (input.tagName === 'SELECT') {
                                input.selectedIndex = 0;
                            }
                        });
                        alert('筛选条件已重置！');
                    });
                }
            });
        });

        // 添加一些动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为风险卡片添加悬停效果
            const riskCards = document.querySelectorAll('.risk-card');
            riskCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 为状态标签添加动画
            const statusTags = document.querySelectorAll('.status-tag');
            statusTags.forEach(tag => {
                tag.style.transition = 'all 0.2s ease';
            });
        });
    </script>
</body>
</html>
